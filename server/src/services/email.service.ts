import nodemailer from "nodemailer";
import dotenv from "dotenv";

dotenv.config();
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5001/api";

console.log("Email service configuration:");
console.log("EMAIL_USER:", process.env.EMAIL_USER);
console.log("EMAIL_PASS:", process.env.EMAIL_PASS ? "Set" : "Not set");

const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 587,
  secure: false, // use TLS
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export const sendContactConfirmation = async (to: string, name: string) => {
  const firstName = name.split(' ')[0];
  const mailOptions = {
    from: `Ankush <PERSON> <${process.env.EMAIL_USER}>`,
    to,
    subject: `Thanks for reaching out, ${firstName}! I'll be in touch soon`,
    html: `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thank You for Contacting Ankush Gupta</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
        <div style="max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 20px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.1); margin-top: 40px; margin-bottom: 40px;">

          <!-- Header Section with Gradient Background -->
          <div style="background: linear-gradient(135deg, #2563eb 0%, #10b981 25%, #8b5cf6 50%, #f59e0b 75%, #2563eb 100%); background-size: 400% 400%; padding: 40px 30px; text-align: center; position: relative; overflow: hidden;">
            <div style="position: relative; z-index: 2;">
              <img src="https://avatars.githubusercontent.com/u/44290249?v=4" alt="Ankush Gupta Profile" style="width: 80px; height: 80px; border-radius: 50%; border: 4px solid rgba(255,255,255,0.3); margin-bottom: 20px; box-shadow: 0 8px 16px rgba(0,0,0,0.2);" />
              <h1 style="color: #ffffff; margin: 0; font-size: 2.2rem; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                Hello, <span style="color: #fbbf24; font-size: 2.4rem;">${firstName}</span>!
              </h1>
              <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 1.1rem; font-weight: 500;">Your message has been received</p>
            </div>
            <!-- Animated background elements -->
            <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"40\" r=\"3\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"80\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"70\" cy=\"70\" r=\"2.5\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"10\" cy=\"60\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></svg>'); opacity: 0.3;"></div>
          </div>

          <!-- Main Content -->
          <div style="padding: 40px 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #1f2937; margin: 0 0 15px 0; font-size: 1.8rem; font-weight: 600;">Thank you for reaching out!</h2>
              <p style="color: #6b7280; font-size: 1.1rem; line-height: 1.6; margin: 0;">I appreciate you taking the time to contact me through my portfolio website.</p>
            </div>

            <!-- Response Timeline -->
            <div style="background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 12px; padding: 25px; margin: 25px 0; border-left: 4px solid #2563eb;">
              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; margin-right: 12px;"></div>
                <h3 style="color: #1f2937; margin: 0; font-size: 1.2rem; font-weight: 600;">Response Timeline</h3>
              </div>
              <p style="color: #4b5563; margin: 0; font-size: 1rem; line-height: 1.5;">I'll get back to you within <strong style="color: #2563eb;">24-48 hours</strong>. For urgent matters, feel free to email me directly at <a href="mailto:${process.env.EMAIL_USER}" style="color: #2563eb; text-decoration: none; font-weight: 600;">${process.env.EMAIL_USER}</a></p>
            </div>

            <!-- Social Media Section -->
            <div style="text-align: center; margin: 35px 0;">
              <h3 style="color: #1f2937; margin: 0 0 20px 0; font-size: 1.3rem; font-weight: 600;">Connect with me</h3>
              <div style="display: inline-block;">
                <!-- LinkedIn -->
                <a href="https://linkedin.com/in/ankushgupta18" target="_blank" rel="noopener noreferrer" style="display: inline-block; margin: 0 8px; padding: 12px; background: #0077b5; border-radius: 50%; text-decoration: none; transition: transform 0.3s ease; box-shadow: 0 4px 8px rgba(0,119,181,0.3);">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwLjQ0NyAyMC40NDJIMTY44OVYxNC44ODNDMTYuODkgMTMuNTU1IDE2Ljg2NiAxMS45NDYgMTUuMDQzIDExLjk0NkMxMy4xODggMTEuOTQ2IDEyLjkwNSAxMy4yOTQgMTIuOTA1IDE0Ljc4NVYyMC40NDJIOS4zNTJWOS4wMDJIMTIuNzY1VjEwLjU2MUgxMi44MUMxMy4yODggOS43OTEgMTQuNDQ4IDguOTY5IDE2LjE4MSA4Ljk2OUMxOS45MjYgOC45NjkgMjAuNDQ4IDExLjU0IDIwLjQ0OCAxNC41ODlWMjAuNDQyWk01LjMzNyA3LjQzM0M0LjE5MyA3LjQzMyAzLjI2NyA2LjUwNyAzLjI2NyA1LjM2M0MzLjI2NyA0LjIxOSA0LjE5MyAzLjI5MyA1LjMzNyAzLjI5M0M2LjQ4MSAzLjI5MyA3LjQwNyA0LjIxOSA3LjQwNyA1LjM2M0M3LjQwNyA2LjUwNyA2LjQ4MSA3LjQzMyA1LjMzNyA3LjQzM1pNNy4xMTkgMjAuNDQySDMuNTU1VjkuMDAySDcuMTE5VjIwLjQ0MloiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=" alt="LinkedIn" style="width: 20px; height: 20px;" />
                </a>

                <!-- GitHub -->
                <a href="https://github.com/AnkushGitRepo" target="_blank" rel="noopener noreferrer" style="display: inline-block; margin: 0 8px; padding: 12px; background: #333; border-radius: 50%; text-decoration: none; transition: transform 0.3s ease; box-shadow: 0 4px 8px rgba(51,51,51,0.3);">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDBDNS4zNzQgMCAwIDUuMzczIDAgMTJDMCAxNy4zMDIgMy40MzggMjEuOCA4LjIwNyAyMy4zODdDOC44MDYgMjMuNDk4IDkuMDI1IDIzLjEyNiA5LjAyNSAyMi44MDFDOS4wMjUgMjIuNTA4IDkuMDE1IDIxLjY5NSA5LjAxIDIwLjUwOEM2LjY3MSAyMS4wMDEgNi4yNDcgMTkuMjQyIDYuMjQ3IDE5LjI0MkM1LjcwNiAxNy44OTYgNC45NTYgMTcuNTI2IDQuOTU2IDE3LjUyNkMzLjg0IDE2Ljk3NSA1LjAzNiAxNi45ODggNS4wMzYgMTYuOTg4QzYuMjY1IDE3LjA3NCA2Ljg0OSAxOC4xMTIgNi44NDkgMTguMTEyQzguMDE5IDIwLjAwOCA5Ljk2MiAxOS40MzggMTAuMDQ3IDE5LjEyMkMxMC4xNTEgMTguMzc1IDEwLjQ2NiAxNy44MDUgMTAuODEgMTcuNDk3QzguMTQ1IDE3LjE4NSA1LjM0NCAxNi4xNyA1LjM0NCAxMS41QzUuMzQ0IDEwLjE5NCA1Ljc5NyA5LjEyNyA2Ljg3IDguMzI0QzYuNzQ1IDguMDEzIDYuMzMgNi43NzUgNi45OTMgNS4xMDZDNi45OTMgNS4xMDYgOC4wODEgNC43NzQgOS45OTggNi4zNTJDMTEuMDQ4IDYuMDc3IDEyLjE1IDUuOTM5IDEzLjI0OCA1LjkzNEMxNC4zNDYgNS45MzkgMTUuNDQ4IDYuMDc3IDE2LjQ5OCA2LjM1MkMxOC40MTUgNC43NzQgMTkuNTAzIDUuMTA2IDE5LjUwMyA1LjEwNkMyMC4xNjYgNi43NzUgMTkuNzUxIDguMDEzIDkuNjI2IDguMzI0QzIwLjY5OSA5LjEyNyAyMS4xNTIgMTAuMTk0IDIxLjE1MiAxMS41QzIxLjE1MiAxNi4xNzYgMTguMzQ1IDE3LjE4MSAxNS42NzYgMTcuNDg5QzE2LjEwNCAxNy44NzMgMTYuNDg2IDE4LjYzIDE2LjQ4NiAxOS43OTNDMTYuNDg2IDIxLjQ3OSAxNi40NzEgMjIuODM4IDE2LjQ3MSAyMi44MDFDMTYuNDcxIDIzLjEyOSAxNi42ODggMjMuNTA0IDE3LjI5NCAyMy4zODZDMjAuNTYgMjEuNzk3IDI0IDEyLjI5OSAyNCAxMkMyNCA1LjM3MyAxOC42MjcgMCAxMiAwWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==" alt="GitHub" style="width: 20px; height: 20px;" />
                </a>

                <!-- Instagram -->
                <a href="https://instagram.com/_ankushg" target="_blank" rel="noopener noreferrer" style="display: inline-block; margin: 0 8px; padding: 12px; background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); border-radius: 50%; text-decoration: none; transition: transform 0.3s ease; box-shadow: 0 4px 8px rgba(225,48,108,0.3);">
                  <img src="data:image/svg+xml;base64,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" alt="Instagram" style="width: 20px; height: 20px;" />
                </a>
              </div>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://github.com/AnkushGitRepo/Portfolio" target="_blank" rel="noopener noreferrer" style="display: inline-block; background: linear-gradient(135deg, #2563eb 0%, #10b981 100%); color: #ffffff; padding: 14px 28px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 1rem; box-shadow: 0 4px 12px rgba(37,99,235,0.3); transition: all 0.3s ease;">
                🚀 Explore My Portfolio
              </a>
            </div>
          </div>

          <!-- Footer -->
          <div style="background: #f9fafb; padding: 25px 30px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 0.9rem;">Best regards,</p>
            <p style="color: #1f2937; margin: 0; font-size: 1.1rem; font-weight: 600;">Ankush Gupta</p>
            <p style="color: #9ca3af; margin: 10px 0 0 0; font-size: 0.8rem;">Full Stack Developer & ML Engineer</p>
            <p style="color: #9ca3af; margin: 5px 0 0 0; font-size: 0.8rem;">📍 Ahmedabad, Gujarat, India</p>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Confirmation email sent:", info.response);
    return info;
  } catch (err) {
    console.error("Error sending confirmation email:", err);
    throw err;
  }
};

export const sendContactNotification = async (formData: any) => {
  const mailOptions = {
    from: `Portfolio Website <${process.env.EMAIL_USER}>`,
    to: process.env.EMAIL_USER,
    subject: `🚀 New Contact: ${formData.name} - ${formData.contactReason}`,
    html: `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Contact Form Submission</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f3f4f6; min-height: 100vh;">
        <div style="max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.1); margin-top: 20px; margin-bottom: 20px;">

          <!-- Header Section -->
          <div style="background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #db2777 100%); padding: 30px; text-align: center; position: relative;">
            <div style="position: relative; z-index: 2;">
              <h1 style="color: #ffffff; margin: 0; font-size: 1.8rem; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                📬 New Contact Form Submission
              </h1>
              <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 1rem;">From your portfolio website</p>
            </div>
            <!-- Background decoration -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"25\" cy=\"25\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"35\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"45\" cy=\"75\" r=\"2.5\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"85\" cy=\"65\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></svg>'); opacity: 0.3;"></div>
          </div>

          <!-- Contact Information Section -->
          <div style="padding: 30px;">
            <!-- Contact Person Info -->
            <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 20px; margin-bottom: 25px; border-left: 4px solid #3b82f6;">
              <h2 style="color: #1f2937; margin: 0 0 15px 0; font-size: 1.4rem; font-weight: 600; display: flex; align-items: center;">
                <span style="background: #3b82f6; color: white; width: 30px; height: 30px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 0.9rem;">👤</span>
                Contact Details
              </h2>

              <div style="display: grid; gap: 12px;">
                <div style="display: flex; align-items: center; padding: 8px 0;">
                  <span style="color: #6b7280; font-weight: 600; min-width: 80px; font-size: 0.9rem;">Name:</span>
                  <span style="color: #1f2937; font-weight: 600; font-size: 1.1rem;">${formData.name}</span>
                </div>

                <div style="display: flex; align-items: center; padding: 8px 0;">
                  <span style="color: #6b7280; font-weight: 600; min-width: 80px; font-size: 0.9rem;">Email:</span>
                  <a href="mailto:${formData.email}" style="color: #3b82f6; text-decoration: none; font-weight: 600; font-size: 1rem;">${formData.email}</a>
                </div>

                <div style="display: flex; align-items: center; padding: 8px 0;">
                  <span style="color: #6b7280; font-weight: 600; min-width: 80px; font-size: 0.9rem;">Reason:</span>
                  <span style="background: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">${formData.contactReason}</span>
                </div>
              </div>
            </div>

            <!-- Message Section -->
            <div style="background: #ffffff; border: 2px solid #e5e7eb; border-radius: 12px; padding: 20px; margin-bottom: 25px;">
              <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 1.2rem; font-weight: 600; display: flex; align-items: center;">
                <span style="background: #10b981; color: white; width: 28px; height: 28px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 0.8rem;">💬</span>
                Message
              </h3>
              <div style="background: #f9fafb; border-radius: 8px; padding: 16px; border-left: 4px solid #10b981;">
                <p style="color: #374151; margin: 0; line-height: 1.6; font-size: 1rem; white-space: pre-wrap;">${formData.message}</p>
              </div>
            </div>

            <!-- Quick Actions -->
            <div style="text-align: center; margin: 25px 0;">
              <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 1.1rem; font-weight: 600;">Quick Actions</h3>
              <div style="display: inline-block;">
                <a href="mailto:${formData.email}?subject=Re: Your inquiry about ${formData.contactReason}&body=Hi ${formData.name},%0D%0A%0D%0AThank you for reaching out through my portfolio website.%0D%0A%0D%0ABest regards,%0D%0AAnkush Gupta" style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 0.95rem; margin: 0 8px; box-shadow: 0 4px 12px rgba(59,130,246,0.3);">
                  📧 Reply to ${formData.name.split(' ')[0]}
                </a>

                <a href="https://gmail.com" target="_blank" rel="noopener noreferrer" style="display: inline-block; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: #ffffff; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 0.95rem; margin: 0 8px; box-shadow: 0 4px 12px rgba(16,185,129,0.3);">
                  📬 Open Gmail
                </a>
              </div>
            </div>

            <!-- Timestamp -->
            <div style="background: #f3f4f6; border-radius: 8px; padding: 15px; text-align: center; margin-top: 20px;">
              <p style="color: #6b7280; margin: 0; font-size: 0.9rem;">
                <strong>Received:</strong> ${new Date().toLocaleString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  timeZoneName: 'short'
                })}
              </p>
              <p style="color: #9ca3af; margin: 5px 0 0 0; font-size: 0.8rem;">This message was sent from your portfolio website contact form</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Notification email sent:", info.response);
    return info;
  } catch (err) {
    console.error("Error sending notification email:", err);
    throw err;
  }
};
