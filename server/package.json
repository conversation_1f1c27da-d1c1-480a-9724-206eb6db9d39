{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "morgan": "^1.10.0", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.12", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}