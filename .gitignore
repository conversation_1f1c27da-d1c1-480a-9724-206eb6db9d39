# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
/client/.next/
# /client/out/

# production
/build
/client/build
/server/dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
/server/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
